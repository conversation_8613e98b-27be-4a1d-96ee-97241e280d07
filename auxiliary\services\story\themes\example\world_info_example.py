# -*- coding: utf-8 -*-
"""World Info功能示例主題"""

THEME_CONFIG = {
    "title": "World Info功能示例",
    "description": "展示如何使用新的World Info功能，包括直接注入到prompt模板和自定義歷史紀錄role",
    "prompt_library": "beilu",  # 使用beilu庫以支持新的world_info_content模板
    "image_url": "https://example.com/image.png",
    "opening_line": "這是一個展示World Info功能的示例故事。",
    "initial_options": [
        "選項1",
        "選項2", 
        "選項3",
        "選項4",
    ],
    "character_sheet": """
# 角色設定
name: 示例角色
age: 20歲
""",
    "custom_world_info_rules": [
        # 示例1：直接注入到prompt模板的world info（支持順序）
        {
            "id": "world_info_prompt_basic",
            "name": "Basic World Info",
            "content": """<基礎世界設定>
這是基礎的世界觀設定，會最先顯示。
</基礎世界設定>""",
            "insertion_target": "prompt_library",  # 注入到prompt模板
            "insertion_id": "world_info_content",   # 對應{world_info_content}變量
            "order": 1,  # 順序：1（較前面）
            "trigger_probability": 1.0,
            "enabled": True,
        },
        {
            "id": "world_info_prompt_advanced",
            "name": "Advanced World Info", 
            "content": """<進階世界設定>
這是進階的世界觀設定，會在基礎設定之後顯示。
</進階世界設定>""",
            "insertion_target": "prompt_library",
            "insertion_id": "world_info_content",
            "order": 5,  # 順序：5（較後面）
            "trigger_probability": 1.0,
            "enabled": True,
        },
        
        # 示例2：基於關鍵字觸發的world info
        {
            "id": "combat_world_info",
            "name": "Combat World Info",
            "content": """<戰鬥系統提示>
檢測到戰鬥相關內容，激活戰鬥系統world info。
戰鬥中需要考慮策略和技能搭配。
</戰鬥系統提示>""",
            "insertion_target": "history",
            "insertion_depth": 0,
            "role": "system",
            "order": 1,  # 在同深度中優先顯示
            "trigger_keywords": ["戰鬥", "攻擊", "技能", "魔法", "戰斗"],  # 關鍵字觸發
            "trigger_probability": 1.0,
            "enabled": True,
        },
        
        # 示例3：多個同深度world info的順序控制
        {
            "id": "world_info_first",
            "name": "First World Info",
            "content": """這是第一條world info（order=1）""",
            "insertion_target": "history",
            "insertion_depth": 1,
            "role": "system",
            "order": 1,  # 在depth=1中最先顯示
            "trigger_probability": 1.0,
            "enabled": True,
        },
        {
            "id": "world_info_second", 
            "name": "Second World Info",
            "content": """這是第二條world info（order=3）""",
            "insertion_target": "history",
            "insertion_depth": 1,
            "role": "system", 
            "order": 3,  # 在depth=1中第二顯示
            "trigger_probability": 1.0,
            "enabled": True,
        },
        {
            "id": "world_info_third",
            "name": "Third World Info", 
            "content": """這是第三條world info（order=5）""",
            "insertion_target": "history",
            "insertion_depth": 1,
            "role": "system",
            "order": 5,  # 在depth=1中最後顯示
            "trigger_probability": 1.0,
            "enabled": True,
        },
        
        # 示例4：複雜的關鍵字觸發組合
        {
            "id": "location_triggered_info",
            "name": "Location Triggered Info",
            "content": """<地點相關信息>
檢測到地點相關描述，提供環境背景信息。
</地點相關信息>""",
            "insertion_target": "history",
            "insertion_depth": 0,
            "role": "assistant",
            "order": 10,  # 在同深度中最後顯示
            "trigger_keywords": ["城市", "森林", "地下城", "房間", "街道"],
            "trigger_probability": 0.8,  # 80%觸發機率
            "enabled": True,
        },
        
        # 示例5：連鎖觸發示例
        {
            "id": "chain_trigger_1",
            "name": "Chain Trigger 1",
            "content": """檢測到魔法相關內容，啟動戰鬥模式。""",
            "insertion_target": "history",
            "insertion_depth": 0,
            "role": "system",
            "order": 1,
            "trigger_keywords": ["魔法", "法術"],
            "trigger_probability": 1.0,
            "enabled": True,
        },
        {
            "id": "chain_trigger_2", 
            "name": "Chain Trigger 2",
            "content": """戰鬥模式已啟動，建議學習防禦技能。""",
            "insertion_target": "history",
            "insertion_depth": 0,
            "role": "system",
            "order": 2,
            "trigger_keywords": ["戰鬥", "戰斗"],
            "trigger_probability": 1.0,
            "enabled": True,
        },
        {
            "id": "chain_trigger_3",
            "name": "Chain Trigger 3", 
            "content": """防禦技能學習中，體力值將得到提升。""",
            "insertion_target": "history",
            "insertion_depth": 0,
            "role": "system",
            "order": 3,
            "trigger_keywords": ["防禦", "防御"],
            "trigger_probability": 1.0,
            "enabled": True,
        },
    ],
}

# -*- coding: utf-8 -*-
import logging
import random
from enum import Enum
from functools import lru_cache
from typing import Any, Dict, List, Optional, Sequence, Tuple, TypedDict, Union

from .cleanup_strategies import apply_cleanup_strategy, get_cleanup_strategy

# 導入新的多卡片庫管理器
from .prompt_libraries import get_available_libraries, get_prompt_library
from .template_processor import process_card_content

logger = logging.getLogger(__name__)


@lru_cache(maxsize=256)
def _process_keywords_cached(keywords_tuple: Tuple[str, ...]) -> List[str]:
    """
    快取關鍵字處理，避免重複運算

    Args:
        keywords_tuple: 關鍵字元組（不可變，可快取）

    Returns:
        處理後的小寫關鍵字列表
    """
    return [
        kw.lower().strip()
        for kw in keywords_tuple
        if isinstance(kw, str) and kw.strip()
    ]


@lru_cache(maxsize=1024)
def _fast_keyword_search(content: str, keywords_tuple: <PERSON><PERSON>[str, ...]) -> bool:
    """
    高效關鍵字搜尋，使用快取和早期終止

    Args:
        content: 要搜尋的內容
        keywords_tuple: 關鍵字元組

    Returns:
        是否找到任何關鍵字
    """
    content_lower = content.lower()
    return any(keyword in content_lower for keyword in keywords_tuple)


class MessageRole(str, Enum):
    """訊息角色列舉，確保類型安全"""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class HistoryMessage(TypedDict):
    """歷史訊息的精確類型定義"""

    role: str
    content: str
    status_block: Optional[str]
    world_info_id: Optional[str]


def _check_keyword_triggers(
    history: Sequence[Union[Dict[str, Any], HistoryMessage]], keywords: List[str]
) -> bool:
    """
    檢查最近兩對消息中是否包含任何指定的關鍵字
    將"角色最近的回復"和"你新發出的消息"視為"一對"消息

    Args:
        history: 歷史記錄列表
        keywords: 要檢查的關鍵字列表

    Returns:
        如果在最近兩對消息中找到任何關鍵字則返回True，否則返回False
    """
    # 早期返回：沒有關鍵字則默認觸發
    if not keywords:
        return True

    # 輸入驗證 - 使用更 Pythonic 的方式
    if not isinstance(history, list) or not isinstance(keywords, list):
        logger.warning(
            f"Invalid input types: history={type(history)}, keywords={type(keywords)}"
        )
        return False

    # 智能關鍵字處理 - 快取優化和早期終止
    keywords_lower = _process_keywords_cached(tuple(keywords))

    if not keywords_lower:
        return True  # 沒有有效關鍵字，默認觸發

    # 直接檢查最近4條消息（約等於兩對），避免複雜的配對邏輯
    # 限制檢查範圍，提升性能
    check_limit = min(4, len(history))
    if check_limit == 0:
        return False

    # 從最新開始檢查，一旦找到就立即返回（早期終止）
    keywords_tuple = tuple(keywords_lower)  # 避免重複轉換

    for i in range(check_limit):
        turn = history[-(i + 1)]  # 從後往前取

        # 構建完整內容
        content = str(turn.get("content", ""))
        status_block = turn.get("status_block")
        if status_block:
            content += f" {status_block}"

        if content and _fast_keyword_search(content, keywords_tuple):
            return True

    return False


def _inject_history_with_world_info_rules(
    messages: List[Dict[str, str]],
    short_term_history: Sequence[Union[Dict[str, Any], HistoryMessage]],
    long_term_summaries: List[str],
    theme_settings: Dict[str, Any],
    format_kwargs: Dict[str, Any],
    library_name: str,
):
    """
    健壯地注入歷史記錄和World Info。
    此版本確保即使歷史記錄長度不足，定義的World Info規則也能被正確插入。
    支持連鎖觸發：world info內容可以觸發其他world info。
    新增：支持長期摘要作為歷史記錄格式注入。
    """
    # 0. 首先注入長期摘要作為歷史記錄格式
    strategy = get_cleanup_strategy(library_name)
    if long_term_summaries:
        for summary in long_term_summaries:
            # 將每個摘要作為assistant角色的歷史記錄注入
            cleaned_summary = apply_cleanup_strategy(
                summary, strategy, cleanup_type="history"
            )
            if cleaned_summary.strip():  # 只有清理後還有內容才注入
                _append_message(
                    messages, {"role": "assistant", "content": cleaned_summary}
                )

    # 1. 處理並分組自定義World Info規則
    custom_rules = theme_settings.get("custom_world_info_rules", [])
    # 只處理要插入歷史的規則
    history_rules = [
        rule
        for rule in custom_rules
        if rule.get("insertion_target", "history") == "history"
    ]

    # 創建一個擴展的歷史記錄，包含原始歷史和已觸發的world info
    extended_history = list(short_term_history)
    rules_by_depth = {}

    if not history_rules:  # 如果沒有規則，直接注入歷史
        # 仍需要注入歷史記錄，只是沒有world info
        history_len = len(short_term_history)

        for i, turn in enumerate(short_term_history):
            if turn.get("role") == "assistant":
                full_content = str(turn.get("content", ""))
                status_block = turn.get("status_block")
                if status_block:
                    full_content += f"\n<status_block>{status_block}</status_block>"
                content = apply_cleanup_strategy(
                    full_content, strategy, cleanup_type="history"
                )
                _append_message(messages, {"role": "assistant", "content": content})
            else:  # role == "user"
                user_content = str(turn.get("content", ""))
                is_last_turn = i == history_len - 1
                if library_name == "beilu" and is_last_turn:
                    user_content = f"<user_input>{user_content}</user_input>"
                _append_message(messages, {"role": "user", "content": user_content})
        return  # 提早返回，避免下面的邏輯

    # 輸入驗證和安全檢查
    max_iterations = theme_settings.get("world_info_chain_depth", 3)
    if not isinstance(max_iterations, int) or max_iterations < 0:
        logger.warning(
            f"Invalid world_info_chain_depth: {max_iterations}, using default 3"
        )
        max_iterations = 3
    if max_iterations > 10:  # 防止設置過大值
        logger.warning(
            f"world_info_chain_depth too large: {max_iterations}, capping at 10"
        )
        max_iterations = 10

    # 預先進行機率檢查，避免重複隨機化
    enabled_rules = []
    for rule in history_rules:
        if not rule.get("enabled", True):
            continue

        # 輸入驗證
        rule_id = rule.get("id")
        if not rule_id or not isinstance(rule_id, str):
            logger.warning(f"Invalid rule id: {rule_id}, skipping rule")
            continue

        trigger_prob = rule.get("trigger_probability", 1.0)
        if not isinstance(trigger_prob, (int, float)) or not (0 <= trigger_prob <= 1):
            logger.warning(
                f"Invalid trigger_probability for rule {rule_id}: {trigger_prob}, using 1.0"
            )
            trigger_prob = 1.0

        if random.random() > trigger_prob:
            continue

        # 驗證insertion_depth
        depth = rule.get("insertion_depth", 0)
        if not isinstance(depth, int):
            logger.warning(
                f"Invalid insertion_depth for rule {rule_id}: {depth}, using 0"
            )
            depth = 0
        if depth < 0:
            logger.warning(
                f"Negative insertion_depth for rule {rule_id}: {depth}, using 0"
            )
            depth = 0

        # 驗證order
        order = rule.get("order", 0)
        if not isinstance(order, (int, float)):
            logger.warning(f"Invalid order for rule {rule_id}: {order}, using 0")
            order = 0

        enabled_rules.append(
            {
                "rule": rule,
                "id": rule_id,
                "depth": depth,
                "order": order,
            }
        )

    # 多輪檢查以支持連鎖觸發
    triggered_rule_ids = set()  # 記錄已觸發的規則ID

    for _iteration in range(max_iterations):
        newly_triggered = []

        for rule_info in enabled_rules:
            rule = rule_info["rule"]
            rule_id = rule_info["id"]

            # 檢查是否已經被觸發過
            if rule_id in triggered_rule_ids:
                continue

            # 檢查關鍵字觸發條件
            trigger_keywords = rule.get("trigger_keywords", [])
            if trigger_keywords:
                if not isinstance(trigger_keywords, list):
                    logger.warning(
                        f"Invalid trigger_keywords for rule {rule_id}: {trigger_keywords}"
                    )
                    continue
                if not _check_keyword_triggers(extended_history, trigger_keywords):
                    continue

            # 安全地處理模板內容
            try:
                content = process_card_content(rule["content"], format_kwargs)
            except Exception as e:
                logger.error(f"Failed to process content for rule {rule_id}: {e}")
                continue

            depth = rule_info["depth"]
            if depth not in rules_by_depth:
                rules_by_depth[depth] = []
            rules_by_depth[depth].append(
                {
                    "content": content,
                    "id": rule_id,
                    "role": rule.get("role", "system"),
                    "order": rule_info["order"],
                }
            )

            # 標記為已觸發
            triggered_rule_ids.add(rule_id)

            # 將新觸發的world info添加到擴展歷史中，供下一輪檢查使用
            newly_triggered.append(
                {
                    "role": rule.get("role", "system"),
                    "content": content,
                    "world_info_id": rule_id,  # 標記這是world info
                }
            )

        # 如果這一輪沒有新觸發的，就結束
        if not newly_triggered:
            break

        # 限制擴展歷史的大小，防止內存無限增長
        if len(extended_history) > 1000:  # 設置合理上限
            logger.warning(
                "Extended history too large, truncating to prevent memory issues"
            )
            extended_history = extended_history[-500:]  # 保留最近500條

        # 將新觸發的添加到擴展歷史中
        extended_history.extend(newly_triggered)

    history_len = len(short_term_history)

    # 尋找需要處理的最大深度
    max_required_depth = max(rules_by_depth.keys()) if rules_by_depth else -1

    # --- 階段一：預先加載 (Prepending) ---
    # 處理所有深度值超出實際歷史長度的規則 (對應「幽靈」歷史)
    # 從最大需要的深度，一直遍歷到實際歷史長度的邊界
    for depth in range(max_required_depth, history_len - 1, -1):
        if depth in rules_by_depth:
            # 按順序排序後注入（數字越大越在下面）
            sorted_rules = sorted(rules_by_depth[depth], key=lambda x: x["order"])
            for rule in sorted_rules:
                world_info_msg = {"role": rule["role"], "content": rule["content"]}
                _append_message(messages, world_info_msg)

    # --- 階段二：交錯注入 (Interleaving) ---
    # 遍歷實際存在的歷史記錄
    for i, turn in enumerate(short_term_history):
        # (A) 先注入當前回合的歷史訊息
        if turn.get("role") == "assistant":
            full_content = str(turn.get("content", ""))
            status_block = turn.get("status_block")
            if status_block:
                full_content += f"\n<status_block>{status_block}</status_block>"
            content = apply_cleanup_strategy(
                full_content, strategy, cleanup_type="history"
            )
            _append_message(messages, {"role": "assistant", "content": content})
        else:  # role == "user"
            user_content = str(turn.get("content", ""))
            # 根據最新需求：只在 beilu 庫中，且是最後一條 user message 時，才添加標籤
            is_last_turn = i == history_len - 1
            if library_name == "beilu" and is_last_turn:
                user_content = f"<user_input>{user_content}</user_input>"

            _append_message(messages, {"role": "user", "content": user_content})

        # (B) 再檢查是否需要在這個歷史訊息之後插入對應深度的 World Info
        depth_from_newest = history_len - 1 - i
        if depth_from_newest in rules_by_depth:
            # 按順序排序後注入（數字越大越在下面）
            sorted_rules = sorted(
                rules_by_depth[depth_from_newest], key=lambda x: x["order"]
            )
            for rule in sorted_rules:
                world_info_msg = {"role": rule["role"], "content": rule["content"]}
                _append_message(messages, world_info_msg)


def build_prompt_messages(
    theme_settings: Dict[str, Any],
    long_term_summaries: List[str],
    short_term_history: Sequence[Union[Dict[str, Any], HistoryMessage]],
    user_input: str,
    user_display_name: str,
    library_name: Optional[str] = None,
) -> List[Dict[str, str]]:
    """
    使用結構化的提示詞卡片庫，動態構建完整的 messages 列表。
    """
    messages = []
    if library_name is None:
        library_name = theme_settings.get("prompt_library")
        if not library_name:
            raise ValueError("...")

    # 獲取的是一個副本，可以安全修改
    prompt_library = [card.copy() for card in get_prompt_library(library_name)]

    # 2. 準備格式化變數。這個字典會被用於所有卡片的 .format() 方法。
    # 注意：long_term_summary_block 已移除，長期摘要現在直接作為歷史記錄注入
    format_kwargs = {
        "user_display_name": user_display_name,
        "character_sheet": theme_settings.get("character_sheet", ""),
        "user_input": user_input,  # 確保 user_input 變數可用
        "status_block_rules": theme_settings.get("status_block_rules", ""),
        "char": theme_settings.get("title", "角色"),  # 動態注入劇本title作為角色名稱
    }

    # --- 新增：預處理 World Info，準備注入到 format_kwargs ---
    custom_rules = theme_settings.get("custom_world_info_rules", [])

    # 新增：從全局提示詞庫中提取有 insertion_depth 參數的卡片，轉換為 world info 格式
    prompt_library_without_injection = []

    for card in prompt_library:
        if not card.get("enabled", False):
            continue

        # 檢查是否有 insertion_depth 參數
        if "insertion_depth" in card:
            # 轉換為 world info 格式，加入到 custom_rules 中
            converted_rule = {
                "id": f"prompt_library_{card['id']}",
                "name": card.get("name", card["id"]),
                "content": card[
                    "content"
                ],  # 內容會在 world info 處理時通過 process_card_content 處理
                "insertion_target": "history",
                "insertion_depth": card["insertion_depth"],
                "role": card["role"],
                "order": card.get("order", 0),
                "trigger_probability": 1.0,  # 全局提示詞庫的卡片總是觸發
                "enabled": True,
            }
            custom_rules.append(converted_rule)
        else:
            # 沒有 insertion_depth 的卡片保持原有處理方式
            prompt_library_without_injection.append(card)

    # 更新 prompt_library 為不需要注入的卡片
    prompt_library = prompt_library_without_injection

    # 先處理prompt_library類型的world info（需要支持關鍵字觸發）
    world_info_by_id = {}

    # 為prompt_library注入準備歷史記錄（包含當前用戶輸入）
    full_history_for_prompt = list(short_term_history) + [
        {"role": "user", "content": user_input}
    ]

    for rule in custom_rules:
        if not rule.get("enabled", True):
            continue
        if rule.get("insertion_target") != "prompt_library":
            continue

        # 輸入驗證
        rule_id = rule.get("id", "unknown")
        trigger_prob = rule.get("trigger_probability", 1.0)
        if not isinstance(trigger_prob, (int, float)) or not (0 <= trigger_prob <= 1):
            logger.warning(
                f"Invalid trigger_probability for rule {rule_id}: {trigger_prob}, using 1.0"
            )
            trigger_prob = 1.0

        if random.random() > trigger_prob:
            continue

        # 檢查關鍵字觸發條件
        trigger_keywords = rule.get("trigger_keywords", [])
        if trigger_keywords:
            if not isinstance(trigger_keywords, list):
                logger.warning(
                    f"Invalid trigger_keywords for rule {rule_id}: {trigger_keywords}"
                )
                continue
            if not _check_keyword_triggers(full_history_for_prompt, trigger_keywords):
                continue

        target_id = rule.get("insertion_id")
        if not target_id or not isinstance(target_id, str):
            logger.warning(f"Invalid insertion_id for rule {rule_id}: {target_id}")
            continue

        if target_id not in world_info_by_id:
            world_info_by_id[target_id] = []

        # 驗證order
        order = rule.get("order", 0)
        if not isinstance(order, (int, float)):
            logger.warning(f"Invalid order for rule {rule_id}: {order}, using 0")
            order = 0

        # 安全地處理內容
        try:
            content = str(rule["content"])  # 確保是字符串
        except Exception as e:
            logger.error(f"Failed to process content for rule {rule_id}: {e}")
            continue

        # 添加順序信息和內容
        world_info_by_id[target_id].append({"content": content, "order": order})

    # 將分組後的 world info 按順序排列並加入 format_kwargs
    for target_id, items in world_info_by_id.items():
        # 按順序排序（數字越大越在下面）
        sorted_items = sorted(items, key=lambda x: x["order"])
        # 提取內容並連接
        contents = [item["content"] for item in sorted_items]
        combined_content = "\n".join(contents)
        # 使用 target_id 作為 format_kwargs 的鍵
        format_kwargs[target_id] = combined_content

    # --- 核心修改開始 ---

    # 1. 將最新的 user_input 暫時加入到歷史記錄中，以便統一處理深度注入
    # 注意：beilu_library 的 user 輸入格式是直接的內容，
    # 而 default_library 則是由卡片庫自己決定格式。
    # 我們在這裡統一將其視為一個 role 為 'user' 的回合。
    augmented_history = list(short_term_history) + [
        {"role": "user", "content": user_input}  # 直接使用原始 user_input
    ]

    # --- 核心修改結束 ---

    for card in prompt_library:
        if not card.get("enabled", False):
            continue

        # ... (處理 condition 的程式碼不變) ...
        condition = card.get("condition")
        if condition:
            try:
                cond_type, cond_value = condition.split(":", 1)
                if (
                    cond_type == "theme_type"
                    and theme_settings.get("status_block_type") != cond_value
                ):
                    continue
            except ValueError:
                logger.warning(
                    "卡片條件格式無效，已跳過 - 卡片ID: %s, 條件: %s",
                    card.get("id", "unknown"),
                    condition,
                )
                continue

        if card["role"] == "marker":
            if card["id"] == "history_marker":
                # 在這裡，我們傳入增強版的歷史記錄
                # _inject_history_with_world_info_rules 內部會根據 library_name
                # 來決定如何格式化 user 角色的 content (例如，是否添加 <interactive_input>)

                # 創建 theme_settings 的副本以避免修改原始對象
                effective_theme_settings = theme_settings.copy()
                # 將合併了 library 規則的 custom_rules 列表更新到副本中
                effective_theme_settings["custom_world_info_rules"] = custom_rules

                _inject_history_with_world_info_rules(
                    messages,
                    augmented_history,  # <-- 使用增強版歷史
                    long_term_summaries,  # <-- 傳入長期摘要
                    effective_theme_settings,  # <-- 使用更新後的設置
                    format_kwargs,  # 傳入 format_kwargs 以便 World Info 模板能使用
                    library_name,
                )
            continue  # Marker 卡片處理完後，跳過後續步驟

        # 處理普通卡片
        # 使用模板处理器处理卡片内容（支持 {{random}} 和 {variable} 語法）
        content = process_card_content(card["content"], format_kwargs)
        _append_message(messages, {"role": card["role"], "content": content})

    return messages


def _append_message(messages: List[Dict[str, str]], new_message: Dict[str, str]):
    """
    一個輔助函數，用於將新訊息添加到列表中。
    根據全域設定，此版本永不合併相同角色的訊息。
    """
    messages.append(new_message)


def get_available_prompt_libraries() -> List[str]:
    """
    獲取所有可用的提示詞卡片庫名稱列表

    Returns:
        可用卡片庫名稱列表
    """
    return get_available_libraries()


def get_prompt_library_info() -> Dict[str, Dict[str, Any]]:
    """
    獲取所有卡片庫的詳細信息

    Returns:
        包含所有卡片庫信息的字典
    """
    from .prompt_libraries import list_libraries_info

    return list_libraries_info()
